#!/usr/bin/env node

const { spawn } = require('child_process');
const chalk = require('chalk');

console.log(chalk.blue('🧪 调试页面验证工具修复效果'));
console.log(chalk.blue('====================================='));

// 测试特定的几个页面，启用详细模式
const testCommand = [
  'node', 'bin/page-validator.js', 'check',
  '/Users/<USER>/works/galaxy/galaxy-vue3-demi',
  '--auto-fix',
  '--no-headless',
  '--verbose',  // 启用详细模式
  '--routes', '/charts/keyboard'  // 只测试这个页面
];

console.log(chalk.gray('🚀 启动页面验证工具...'));
console.log(chalk.gray(`   命令: ${testCommand.join(' ')}`));

const child = spawn(testCommand[0], testCommand.slice(1), {
  stdio: 'inherit',
  cwd: process.cwd()
});

// 处理进程退出
child.on('exit', (code) => {
  console.log(chalk.blue('\n====================================='));
  if (code === 0) {
    console.log(chalk.green('✅ 测试完成，进程正常退出'));
  } else {
    console.log(chalk.red(`❌ 测试失败，退出代码: ${code}`));
  }
  
  console.log(chalk.blue('📋 调试要点检查:'));
  console.log(chalk.gray('   1. 错误过滤逻辑是否正确工作'));
  console.log(chalk.gray('   2. AI 响应解析失败的具体原因'));
  console.log(chalk.gray('   3. 修复后的错误计数是否准确'));
  console.log(chalk.gray('   4. 详细的错误信息是否有助于诊断问题'));
});

// 处理中断信号
process.on('SIGINT', () => {
  console.log(chalk.yellow('\n⚠️  收到中断信号，正在终止测试...'));
  child.kill('SIGINT');
  process.exit(0);
});
